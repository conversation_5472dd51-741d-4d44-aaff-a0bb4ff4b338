<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753605043421_60jbp1s5d" time="2025/07/27 16:30">
    <content>
      用户正在新窗口测试AI团队协作流程，如果团队成员没有严格按照建立的协作流程执行（需求分析→架构设计→开发实现→质量验证→项目交付），用户会回来&quot;鞭策&quot;我。需要准备诊断问题、强化违规角色、完善监督机制、修复协作漏洞的解决方案。
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754231255843_zaag6qz85" time="2025/08/03 22:27">
    <content>
      用户需要将七人AI团队（Lucas、Sophia、Marcus、Ryan、David、Tony、Alex）的能力内化到单个AI Agent中，通过系统提示词让AI具备所有角色的核心能力和协作流程，优先使用Context7环境，严格执行需求确认→产品策略→系统架构→开发实现→数据验证→项目交付的流程。用户的角色文件分布在.promptx/resource/role/process-supervisor（Alex）和.promptx/resource/domain/目录中的其他六个角色。
    </content>
    <tags>#流程管理</tags>
  </item>
</memory>